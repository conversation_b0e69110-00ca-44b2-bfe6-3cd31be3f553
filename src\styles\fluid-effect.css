/* Стили для эффекта fluid */
canvas {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
}

/* Стили для секций, чтобы они были поверх эффекта */
section {
  position: relative;
  z-index: 10;
}

/* Стили для основного контента */
main {
  position: relative;
  z-index: 10;
}

/* Стили для header и footer */
header {
  position: relative;
  z-index: 50;
}

footer {
  position: relative;
  z-index: 10;
}

/* Стили для карточек */
.card-above-fluid {
  position: relative;
  background-color: rgba(255, 255, 255, 0.9) !important;
}

.dark .card-above-fluid {
  background-color: rgba(31, 41, 55, 0.9) !important;
}

/* Улучшение производительности на мобильных устройствах */
@media (max-width: 768px) {
  canvas {
    opacity: 0.9;
  }
}
