import React, { useEffect, useRef } from "react";
import WebGLFluidEnhanced from "webgl-fluid-enhanced";

const FluidEffect: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<WebGLFluidEnhanced | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Инициализация симуляции
    simulationRef.current = new WebGLFluidEnhanced(containerRef.current);

    // Настройка параметров
    simulationRef.current.setConfig({
      colorPalette: ["#3b82f6", "#6366f1", "#8b5cf6", "#d946ef", "#ec4899"],
      transparent: true,
      brightness: 0.7,
      densityDissipation: 0.98,
      velocityDissipation: 0.99,
      pressure: 0.8,
      pressureIterations: 20,
      curl: 30,
      splatRadius: 0.25,
      splatForce: 6000,
      shading: true,
      colorful: true,
      colorUpdateSpeed: 10,
      hover: true,
      bloom: true,
      bloomIterations: 8,
      bloomResolution: 256,
      bloomIntensity: 0.4,
      bloomThreshold: 0.8,
      bloomSoftKnee: 0.7,
      sunrays: true,
      sunraysResolution: 196,
      sunraysWeight: 0.6,
    });

    // Запуск симуляции
    simulationRef.current.start();

    // Создаем начальные всплески
    simulationRef.current.multipleSplats(5);

    // Добавляем обработчик для пропуска кликов к элементам под canvas
    const canvas = containerRef.current.querySelector("canvas");
    if (canvas) {
      const handleCanvasClick = (e: MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        // Временно скрываем canvas
        canvas.style.pointerEvents = "none";

        // Получаем элемент под курсором
        const elementBelow = document.elementFromPoint(e.clientX, e.clientY);

        // Восстанавливаем pointer-events
        canvas.style.pointerEvents = "auto";

        // Проверяем, является ли элемент интерактивным
        if (elementBelow) {
          const isClickable =
            elementBelow.tagName === "A" ||
            elementBelow.tagName === "BUTTON" ||
            elementBelow.tagName === "INPUT" ||
            elementBelow.tagName === "SELECT" ||
            elementBelow.tagName === "TEXTAREA" ||
            elementBelow.closest("a, button, input, select, textarea, [role='button'], [onclick], .cursor-pointer") ||
            elementBelow.hasAttribute("onclick") ||
            elementBelow.classList.contains("cursor-pointer") ||
            getComputedStyle(elementBelow).cursor === "pointer";

          if (isClickable) {
            const clickableElement =
              elementBelow.tagName === "A" ||
              elementBelow.tagName === "BUTTON" ||
              elementBelow.tagName === "INPUT" ||
              elementBelow.tagName === "SELECT" ||
              elementBelow.tagName === "TEXTAREA"
                ? elementBelow
                : elementBelow.closest(
                    "a, button, input, select, textarea, [role='button'], [onclick], .cursor-pointer"
                  );

            if (clickableElement) {
              // Создаем новое событие клика
              const clickEvent = new MouseEvent("click", {
                bubbles: true,
                cancelable: true,
                clientX: e.clientX,
                clientY: e.clientY,
                button: e.button,
                buttons: e.buttons,
              });

              clickableElement.dispatchEvent(clickEvent);
            }
          }
        }
      };

      canvas.addEventListener("click", handleCanvasClick);

      // Сохраняем ссылку на обработчик для очистки
      (canvas as any)._fluidClickHandler = handleCanvasClick;
    }

    return () => {
      if (simulationRef.current) {
        simulationRef.current.stop();
        simulationRef.current = null;
      }

      // Очищаем обработчик событий
      const canvas = containerRef.current?.querySelector("canvas");
      if (canvas && (canvas as any)._fluidClickHandler) {
        canvas.removeEventListener("click", (canvas as any)._fluidClickHandler);
        delete (canvas as any)._fluidClickHandler;
      }
    };
  }, []);

  return (
    <div
      className="fixed left-0 top-0 w-full h-full"
      style={{
        zIndex: 15,
        pointerEvents: "none",
      }}
    >
      <div ref={containerRef} className="w-full h-full" style={{ pointerEvents: "auto" }} />
    </div>
  );
};

export default FluidEffect;
