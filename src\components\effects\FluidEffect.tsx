import React, { useEffect, useRef } from "react";
import WebGLFluidEnhanced from "webgl-fluid-enhanced";

const FluidEffect: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<WebGLFluidEnhanced | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Инициализация симуляции
    simulationRef.current = new WebGLFluidEnhanced(containerRef.current);

    // Настройка параметров
    simulationRef.current.setConfig({
      colorPalette: ["#3b82f6", "#6366f1", "#8b5cf6", "#d946ef", "#ec4899"],
      transparent: true,
      brightness: 0.7,
      densityDissipation: 0.98,
      velocityDissipation: 0.99,
      pressure: 0.8,
      pressureIterations: 20,
      curl: 30,
      splatRadius: 0.25,
      splatForce: 6000,
      shading: true,
      colorful: true,
      colorUpdateSpeed: 10,
      hover: true,
      bloom: true,
      bloomIterations: 8,
      bloomResolution: 256,
      bloomIntensity: 0.4,
      bloomThreshold: 0.8,
      bloomSoftKnee: 0.7,
      sunrays: true,
      sunraysResolution: 196,
      sunraysWeight: 0.6,
    });

    // Запуск симуляции
    simulationRef.current.start();

    // Создаем начальные всплески
    simulationRef.current.multipleSplats(5);

    // Добавляем обработчик для пропуска кликов к элементам под canvas
    const canvas = containerRef.current.querySelector("canvas");
    if (canvas) {
      canvas.addEventListener("click", (e) => {
        // Временно скрываем canvas
        canvas.style.pointerEvents = "none";

        // Получаем элемент под курсором
        const elementBelow = document.elementFromPoint(e.clientX, e.clientY);

        // Восстанавливаем pointer-events
        canvas.style.pointerEvents = "auto";

        // Если под курсором есть кликабельный элемент, кликаем по нему
        if (
          elementBelow &&
          (elementBelow.tagName === "A" || elementBelow.tagName === "BUTTON" || elementBelow.closest("a, button"))
        ) {
          const clickableElement =
            elementBelow.tagName === "A" || elementBelow.tagName === "BUTTON"
              ? elementBelow
              : elementBelow.closest("a, button");

          if (clickableElement) {
            (clickableElement as HTMLElement).click();
          }
        }
      });
    }

    return () => {
      if (simulationRef.current) {
        simulationRef.current.stop();
        simulationRef.current = null;
      }
    };
  }, []);

  return (
    <div
      className="fixed left-0 top-0 w-full h-full"
      style={{
        zIndex: 15,
        pointerEvents: "none",
      }}
    >
      <div ref={containerRef} className="w-full h-full" style={{ pointerEvents: "auto" }} />
    </div>
  );
};

export default FluidEffect;
