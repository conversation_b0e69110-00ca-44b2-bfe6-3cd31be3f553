{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-4life", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings=0", "lint:watch": "esw --ext .js,.jsx,.ts,.tsx --format=codeframe --watch src", "preview": "vite preview", "dev:local": "vite --host ************ --port 3000", "dev:network": "vite --host 0.0.0.0 --port 3000"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "framer-motion": "^11.18.2", "lenis": "^1.3.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.51.0", "react-icons": "^5.5.0", "react-router-dom": "^6.23.0", "react-scroll-parallax": "^3.4.5", "react-tilt": "^1.0.0", "webgl-fluid-enhanced": "^0.8.0"}, "devDependencies": {"@emotion/babel-plugin": "^11.13.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-helmet-async": "^1.0.1", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^6.3.5", "vite-plugin-optimize-css-modules": "^1.2.0"}}